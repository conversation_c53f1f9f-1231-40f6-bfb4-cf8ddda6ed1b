This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  1 SEP 2025 15:30
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**./LNNL_MJO_E3SM_Proposal_Manus.tex
(LNNL_MJO_E3SM_Proposal_Manus.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen142
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count268
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count269
\leftroot@=\count270
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count271
\DOTSCASE@=\count272
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count273
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count274
\dotsspace@=\muskip17
\c@parentequation=\count275
\dspbrk@lvl=\count276
\tag@help=\toks20
\row@=\count277
\column@=\count278
\maxfields@=\count279
\andhelp@=\toks21
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count280
\Gm@cntv=\count281
\c@Gm@tempcnt=\count282
\Gm@bindingoffset=\dimen153
\Gm@wd@mp=\dimen154
\Gm@odd@mp=\dimen155
\Gm@even@mp=\dimen156
\Gm@layoutwidth=\dimen157
\Gm@layoutheight=\dimen158
\Gm@layouthoffset=\dimen159
\Gm@layoutvoffset=\dimen160
\Gm@dimlist=\toks25

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen161
\Hy@linkcounter=\count285
\Hy@pagecounter=\count286

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count287

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count288
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen162

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count289
\Field@Width=\dimen163
\Fld@charsize=\dimen164
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count290
\c@Item=\count291
\c@Hfootnote=\count292
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count293
\c@bookmark@seq@number=\count294

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count295
\l__pdf_internal_box=\box54
) (LNNL_MJO_E3SM_Proposal_Manus.aux)
\openout1 = `LNNL_MJO_E3SM_Proposal_Manus.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count296
\scratchdimen=\dimen165
\scratchbox=\box55
\nofMPsegments=\count297
\nofMParguments=\count298
\everyMPshowfont=\toks26
\MPscratchCnt=\count299
\MPscratchDim=\dimen166
\MPnumerator=\count300
\makeMPintoPDFobject=\count301
\everyMPtoPDFconversion=\toks27
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 14.
(LNNL_MJO_E3SM_Proposal_Manus.out) (LNNL_MJO_E3SM_Proposal_Manus.out)
\@outlinefile=\write3
\openout3 = `LNNL_MJO_E3SM_Proposal_Manus.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 17.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 17.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (18.32933pt too wide) in paragraph at lines 22--23
\OT1/cmr/m/n/10 Understanding and ac-cu-rately pre-dict-ing trop-i-cal at-mo-sp
heric vari-abil-ity is paramount for im-prov-ing subseasonal-
 []



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]

[2]

[3]

[4]
Overfull \hbox (7.66241pt too wide) in paragraph at lines 88--89
[]\OT1/cmr/m/n/10 These ob-jec-tives are in-ter-con-nected and will col-lec-tiv
ely ad-vance our un-der-stand-ing of tropical-stratospheric
 []



[5]

[6]

[7]
Overfull \hbox (225.35777pt too wide) in paragraph at lines 159--160
\OT1/cmr/m/n/10 14, no. 2, p. e2021MS002842, 2022. (Pa-per: []JAdvModelEarthSys
t-2022-Kim-TheMaddenJulianOscillationintheEnergyExascaleEarthSystemModelVersion
1.pdf[]) 
 []



[8]
Overfull \hbox (225.35777pt too wide) in paragraph at lines 189--190
\OT1/cmr/m/n/10 14, no. 2, p. e2021MS002842, 2022. (Pa-per: []JAdvModelEarthSys
t-2022-Kim-TheMaddenJulianOscillationintheEnergyExascaleEarthSystemModelVersion
1.pdf[]) 
 []


Overfull \hbox (225.35777pt too wide) in paragraph at lines 209--210
\OT1/